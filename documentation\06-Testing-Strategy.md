# Testing Strategy
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**QA Team:** Development Team  
**Project:** LumusAI  

---

## Table of Contents

1. [Testing Overview](#1-testing-overview)
2. [Test Strategy](#2-test-strategy)
3. [Test Types](#3-test-types)
4. [Test Environment](#4-test-environment)
5. [Test Data Management](#5-test-data-management)
6. [Test Automation](#6-test-automation)
7. [Performance Testing](#7-performance-testing)
8. [Security Testing](#8-security-testing)

---

## 1. Testing Overview

### 1.1 Purpose
This document outlines the comprehensive testing strategy for LumusAI, ensuring the system meets all functional and non-functional requirements while maintaining high quality and reliability.

### 1.2 Testing Objectives
- Verify all functional requirements are implemented correctly
- Ensure system performance meets specified requirements
- Validate security measures and data protection
- Confirm system reliability and error handling
- Validate API functionality and integration points

### 1.3 Scope
**In Scope:**
- Unit testing of all components
- Integration testing of API endpoints
- End-to-end testing of document processing workflows
- Performance and load testing
- Security and vulnerability testing
- Error handling and recovery testing

**Out of Scope:**
- Third-party service testing (OpenAI API)
- Infrastructure testing (Docker, hosting platforms)
- Browser compatibility testing (no UI)

### 1.4 Testing Principles
- **Test-Driven Development:** Write tests before implementation
- **Continuous Testing:** Automated testing in CI/CD pipeline
- **Risk-Based Testing:** Focus on high-risk areas
- **Data-Driven Testing:** Use diverse test datasets
- **Regression Testing:** Ensure changes don't break existing functionality

## 2. Test Strategy

### 2.1 Testing Approach

**Pyramid Testing Model:**
```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/__________\ Unit Tests (Many)
```

**Testing Phases:**
1. **Unit Testing:** Individual component testing
2. **Integration Testing:** Component interaction testing
3. **System Testing:** Complete system functionality
4. **Acceptance Testing:** Business requirement validation

### 2.2 Test Levels

#### 2.2.1 Unit Testing (70% of tests)
- Test individual functions and methods
- Mock external dependencies
- Fast execution and immediate feedback
- High code coverage (minimum 80%)

#### 2.2.2 Integration Testing (20% of tests)
- Test component interactions
- API endpoint testing
- Database integration (if applicable)
- External service integration

#### 2.2.3 End-to-End Testing (10% of tests)
- Complete workflow testing
- Real document processing scenarios
- User journey validation
- Cross-system integration

### 2.3 Test Categories

**Functional Testing:**
- Document processing accuracy
- API endpoint functionality
- Data validation and transformation
- Error handling and recovery

**Non-Functional Testing:**
- Performance and load testing
- Security and vulnerability testing
- Reliability and availability testing
- Scalability and concurrency testing

## 3. Test Types

### 3.1 Unit Tests

#### 3.1.1 Component Testing
**Document Processors:**
```python
# Test CV processor
def test_cv_processor_extract_personal_info():
    processor = CVProcessor(mock_openai_client, mock_langchain_client)
    cv_text = "John Doe\nEmail: <EMAIL>\nPhone: ******-0123"
    
    result = processor.extract_personal_info(cv_text)
    
    assert result["name"] == "John Doe"
    assert result["email"] == "<EMAIL>"
    assert result["phone"] == "******-0123"

# Test invoice processor
def test_invoice_processor_calculate_total():
    processor = InvoiceProcessor(mock_openai_client, mock_langchain_client)
    items = [
        {"quantity": 2, "unit_price": 10.0},
        {"quantity": 1, "unit_price": 15.0}
    ]
    
    total = processor.calculate_total(items)
    
    assert total == 35.0
```

**Utility Functions:**
```python
def test_pdf_text_extraction():
    pdf_path = "test_files/sample.pdf"
    
    text = extract_pdf_text(pdf_path)
    
    assert text is not None
    assert len(text) > 0
    assert "expected content" in text.lower()

def test_file_validation():
    valid_file = create_mock_file("test.pdf", "application/pdf")
    invalid_file = create_mock_file("test.exe", "application/exe")
    
    assert validate_file_type(valid_file) == True
    assert validate_file_type(invalid_file) == False
```

#### 3.1.2 Data Model Testing
```python
def test_cv_model_validation():
    cv_data = {
        "personal_info": {
            "name": "John Doe",
            "email": "invalid-email"  # Invalid email format
        }
    }
    
    with pytest.raises(ValidationError):
        CV(**cv_data)

def test_invoice_model_serialization():
    invoice = InvoicePurchase(
        invoice_number="INV-001",
        invoice_type="Purchase",
        description="Test invoice"
    )
    
    json_data = invoice.model_dump_json()
    restored = InvoicePurchase.model_validate_json(json_data)
    
    assert restored.invoice_number == "INV-001"
```

### 3.2 Integration Tests

#### 3.2.1 API Endpoint Testing
```python
@pytest.mark.asyncio
async def test_process_cv_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as client:
        files = {"file": ("resume.pdf", pdf_content, "application/pdf")}
        data = {"action": "cv"}
        
        response = await client.post("/process", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "success"
        assert "personal_info" in result["data"]

@pytest.mark.asyncio
async def test_health_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/health")
        
        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "ok"
        assert "system_metrics" in result
```

#### 3.2.2 Service Integration Testing
```python
def test_langchain_client_integration():
    client = LangChainClient(api_key, api_version, endpoint, model)
    test_prompt = "Extract name from: John Doe, Software Engineer"
    
    response = client.get_structured_data(PersonalInfo, test_prompt)
    
    assert response is not None
    assert response.name == "John Doe"

def test_file_processing_integration():
    processor = CVProcessor(openai_client, langchain_client)
    test_file = create_test_pdf_file()
    
    result = processor.process(test_file, None)
    
    assert result["status"] == "success"
    assert "data" in result
```

### 3.3 End-to-End Tests

#### 3.3.1 Complete Workflow Testing
```python
@pytest.mark.e2e
async def test_complete_cv_processing_workflow():
    # Upload CV file
    cv_file = load_test_file("sample_cv.pdf")
    
    # Process document
    response = await process_document("cv", cv_file)
    
    # Validate response structure
    assert response["status"] == "success"
    cv_data = response["data"]
    
    # Validate extracted data
    assert cv_data["personal_info"]["name"] is not None
    assert cv_data["personal_info"]["email"] is not None
    assert len(cv_data["work_experience"]) > 0
    assert len(cv_data["skills"]) > 0
    
    # Validate metadata
    assert response["metadata"]["processing_time"] > 0
    assert response["metadata"]["token_usage"]["total_tokens"] > 0

@pytest.mark.e2e
async def test_concurrent_processing():
    # Submit multiple documents simultaneously
    tasks = []
    for i in range(4):
        task = process_document("cv", load_test_file(f"cv_{i}.pdf"))
        tasks.append(task)
    
    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks)
    
    # Validate all processed successfully
    for result in results:
        assert result["status"] == "success"
```

### 3.4 Error Handling Tests

```python
def test_invalid_file_format():
    invalid_file = create_mock_file("test.txt", "text/plain")
    
    response = process_document("cv", invalid_file)
    
    assert response["status"] == "error"
    assert "UNSUPPORTED_FORMAT" in response["error"]["code"]

def test_api_service_failure():
    # Mock OpenAI service failure
    with mock.patch('openai.ChatCompletion.create') as mock_openai:
        mock_openai.side_effect = OpenAIError("Service unavailable")
        
        response = process_document("cv", valid_cv_file)
        
        assert response["status"] == "error"
        assert "AI_SERVICE_ERROR" in response["error"]["code"]

def test_concurrent_limit_exceeded():
    # Submit more tasks than concurrent limit
    tasks = []
    for i in range(10):  # Exceeds default limit of 4
        task = process_document("cv", load_test_file(f"cv_{i}.pdf"))
        tasks.append(task)
    
    # Some tasks should be queued
    health_response = get_health_status()
    assert health_response["tasks"]["waiting_count"] > 0
```

## 4. Test Environment

### 4.1 Test Environment Setup

**Development Environment:**
```bash
# Setup test environment
python -m venv test_env
source test_env/bin/activate
pip install -r requirements.txt
pip install pytest pytest-asyncio pytest-cov

# Configure test environment variables
export API_KEY=test_api_key
export API_VERSION=2023-05-15
export AZURE_ENDPOINT=https://test-endpoint.openai.azure.com/
export MODEL=gpt-4-vision-preview
export MAX_CONCURRENT_TASKS=2
```

**Docker Test Environment:**
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  lumusai-test:
    build: .
    environment:
      - API_KEY=${TEST_API_KEY}
      - API_VERSION=${TEST_API_VERSION}
      - AZURE_ENDPOINT=${TEST_AZURE_ENDPOINT}
      - MODEL=${TEST_MODEL}
      - MAX_CONCURRENT_TASKS=2
    volumes:
      - ./test_files:/app/test_files
      - ./tests:/app/tests
    command: pytest tests/ -v
```

### 4.2 Test Configuration

**pytest Configuration:**
```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    security: Security tests
```

**Test Settings:**
```python
# conftest.py
import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient
from main import app

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture
async def async_client():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def mock_openai_client():
    # Mock OpenAI client for testing
    pass

@pytest.fixture
def test_files():
    return {
        "cv_pdf": "test_files/sample_cv.pdf",
        "invoice_pdf": "test_files/sample_invoice.pdf",
        "legal_pdf": "test_files/sample_legal.pdf"
    }
```

## 5. Test Data Management

### 5.1 Test Data Strategy

**Test Data Categories:**
- **Synthetic Data:** Generated test documents
- **Anonymized Real Data:** Real documents with PII removed
- **Edge Cases:** Malformed or unusual documents
- **Negative Cases:** Invalid or corrupted files

**Test File Organization:**
```
test_files/
├── cv/
│   ├── valid/
│   │   ├── cv_simple.pdf
│   │   ├── cv_complex.pdf
│   │   └── cv_multilingual.pdf
│   ├── invalid/
│   │   ├── cv_corrupted.pdf
│   │   └── cv_empty.pdf
│   └── edge_cases/
│       ├── cv_very_long.pdf
│       └── cv_minimal.pdf
├── invoices/
│   ├── purchase/
│   ├── utility/
│   └── delivery_tickets/
└── legal/
    ├── tutela_contestacion/
    ├── tutela_fallo/
    └── tutela_correo/
```

### 5.2 Test Data Generation

**Synthetic CV Generation:**
```python
def generate_test_cv(complexity="simple"):
    cv_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "experience": generate_work_experience(complexity),
        "education": generate_education(complexity),
        "skills": generate_skills(complexity)
    }
    return create_pdf_from_data(cv_data)

def generate_work_experience(complexity):
    if complexity == "simple":
        return [{"company": "Test Corp", "position": "Developer"}]
    elif complexity == "complex":
        return generate_multiple_experiences()
```

### 5.3 Data Privacy and Security

**Data Handling Guidelines:**
- No real PII in test data
- Anonymize any real documents used
- Secure storage of test data
- Regular cleanup of temporary test files

## 6. Test Automation

### 6.1 Continuous Integration

**GitHub Actions Workflow:**
```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.12.7
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run unit tests
      run: pytest tests/unit/ -v --cov=.
    
    - name: Run integration tests
      run: pytest tests/integration/ -v
      env:
        API_KEY: ${{ secrets.TEST_API_KEY }}
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

### 6.2 Test Execution

**Local Test Execution:**
```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m e2e

# Run with coverage
pytest --cov=. --cov-report=html

# Run performance tests
pytest -m slow --durations=10
```

**Automated Test Scheduling:**
```bash
# Daily regression tests
0 2 * * * cd /opt/lumusai && pytest tests/ --junitxml=results.xml

# Weekly performance tests
0 3 * * 0 cd /opt/lumusai && pytest -m slow --durations=0
```

## 7. Performance Testing

### 7.1 Performance Test Strategy

**Performance Metrics:**
- Response time (95th percentile < 60 seconds)
- Throughput (100+ documents/hour)
- Concurrent processing (4+ simultaneous tasks)
- Resource utilization (CPU, memory)

**Load Testing Scenarios:**
```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

async def load_test_concurrent_processing():
    start_time = time.time()
    
    # Submit multiple documents
    tasks = []
    for i in range(10):
        task = process_document("cv", load_test_file(f"cv_{i}.pdf"))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Validate performance
    assert total_time < 300  # 5 minutes for 10 documents
    assert all(r["status"] == "success" for r in results)

def stress_test_memory_usage():
    # Process large documents
    large_file = create_large_test_file(size_mb=45)
    
    initial_memory = get_memory_usage()
    
    result = process_document("cv", large_file)
    
    final_memory = get_memory_usage()
    memory_increase = final_memory - initial_memory
    
    # Memory should not increase significantly
    assert memory_increase < 100  # MB
    assert result["status"] == "success"
```

### 7.2 Performance Monitoring

**Metrics Collection:**
```python
def test_response_time_monitoring():
    response_times = []
    
    for i in range(50):
        start_time = time.time()
        result = process_document("cv", standard_cv_file)
        end_time = time.time()
        
        response_times.append(end_time - start_time)
    
    # Calculate statistics
    avg_time = sum(response_times) / len(response_times)
    p95_time = sorted(response_times)[int(0.95 * len(response_times))]
    
    assert avg_time < 30  # Average under 30 seconds
    assert p95_time < 60  # 95th percentile under 60 seconds
```

## 8. Security Testing

### 8.1 Security Test Cases

**Input Validation Testing:**
```python
def test_malicious_file_upload():
    # Test with potentially malicious files
    malicious_files = [
        create_file_with_script("malicious.pdf"),
        create_oversized_file("huge.pdf", size_gb=1),
        create_file_with_special_chars("special_chars.pdf")
    ]
    
    for file in malicious_files:
        response = process_document("cv", file)
        assert response["status"] == "error"

def test_sql_injection_attempts():
    # Test with SQL injection patterns in form data
    injection_attempts = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "<script>alert('xss')</script>"
    ]
    
    for attempt in injection_attempts:
        response = process_document(attempt, valid_cv_file)
        assert response["status"] == "error"
```

**API Security Testing:**
```python
def test_api_rate_limiting():
    # Rapid fire requests to test rate limiting
    responses = []
    for i in range(100):
        response = make_api_request("/health")
        responses.append(response.status_code)
    
    # Should eventually get rate limited
    assert 429 in responses  # Too Many Requests

def test_cors_configuration():
    response = make_api_request("/health", 
                               headers={"Origin": "https://malicious-site.com"})
    
    # Validate CORS headers
    assert "Access-Control-Allow-Origin" in response.headers
```

### 8.2 Vulnerability Testing

**Dependency Scanning:**
```bash
# Check for known vulnerabilities
pip-audit

# Security linting
bandit -r .

# Container security scanning
docker run --rm -v $(pwd):/app clair-scanner
```

**Penetration Testing:**
```python
def test_file_path_traversal():
    # Test for directory traversal vulnerabilities
    malicious_paths = [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        "/etc/shadow"
    ]
    
    for path in malicious_paths:
        response = process_document("cv", None, data=path)
        assert response["status"] == "error"
        assert "path traversal" not in response.get("error", {}).get("details", "").lower()
```

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon testing strategy changes
